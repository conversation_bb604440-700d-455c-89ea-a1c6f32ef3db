using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Persistence.Configurations;

public class FeatureFlagConfiguration : IEntityTypeConfiguration<FeatureFlag>
{
    public void Configure(EntityTypeBuilder<FeatureFlag> builder)
    {
        builder.ToTable("FeatureFlags");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Key)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(f => f.Description)
            .HasMaxLength(500);

        builder.Property(f => f.Type)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(f => f.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(f => f.StartDate);

        builder.Property(f => f.EndDate);

        builder.Property(f => f.TargetAudience)
            .HasMaxLength(200);

        builder.Property(f => f.ABTestConfiguration)
            .HasMaxLength(1000);

        builder.Property(f => f.DefaultValue)
            .HasMaxLength(500);

        builder.Property(f => f.Variants)
            .HasMaxLength(1000);

        // Configure MenuId as optional foreign key to UserManagement service
        builder.Property(f => f.MenuId)
            .IsRequired(false); // Make it optional

        // Configure UserType as optional string for filtering
        builder.Property(f => f.UserType)
            .HasMaxLength(50)
            .IsRequired(false); // Make it optional

        // Configure the Metadata property as JSON (optional)
        builder.Property(f => f.Metadata)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => string.IsNullOrEmpty(v) ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null))
            .HasColumnType("jsonb") // PostgreSQL JSONB type
            .IsRequired(false); // Make it optional

        builder.Property(f => f.RolloutPercentage)
            .HasDefaultValue(0);

        builder.Property(f => f.CreatedAt)
            .IsRequired();

        builder.Property(f => f.UpdatedAt);

        // Indexes
        builder.HasIndex(f => f.Key)
            .IsUnique();

        builder.HasIndex(f => f.Name);

        builder.HasIndex(f => f.Type);

        builder.HasIndex(f => f.Status);

        builder.HasIndex(f => f.MenuId);

        // Relationships
        builder.HasMany(f => f.Rules)
            .WithOne()
            .HasForeignKey("FeatureFlagId")
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(f => f.UsageHistory)
            .WithOne()
            .HasForeignKey("FeatureFlagId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}
