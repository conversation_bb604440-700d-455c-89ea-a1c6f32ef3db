using System;
using System.Linq;
using System.Threading.Tasks;
using Identity.Domain.Services;
using Identity.Application.Permissions.Commands.CreatePermission;
using Identity.Application.Permissions.Commands.UpdatePermission;
using Identity.Application.Permissions.Commands.DeletePermission;
using Identity.Application.Permissions.Queries.GetAllPermissionsFromDb;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Identity.API.Controllers
{
    //[Authorize]
    [AllowAnonymous]
    public class PermissionsController : BaseController
    {
        /// <summary>
        /// Get all permissions grouped by category
        /// </summary>
        [AllowAnonymous]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllPermissions(
            [FromQuery] string category = null)
        {
            var permissions = PermissionRegistry.GetAllPermissions();

            if (!string.IsNullOrEmpty(category))
            {
                permissions = permissions.Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
            }

            var result = permissions.Select(p => new
            {
                p.Id,
                p.Name,
                p.Description,
                p.Category,
                p.Resource,
                p.Action
            }).ToList();

            return Ok(result);
        }

        /// <summary>
        /// Get permissions grouped by category
        /// </summary>
        [AllowAnonymous]
        [HttpGet("by-category")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        //[ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetPermissionsByCategory()
        {
            var groupedPermissions = PermissionRegistry.GetPermissionsByCategoryGrouped();

            var result = groupedPermissions.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.Category,
                    p.Resource,
                    p.Action
                }).ToList()
            );

            return Task.FromResult<IActionResult>(Ok(result));
        }

        /// <summary>
        /// Get all categories
        /// </summary>
        [HttpGet("categories")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetAllCategories()
        {
            var categories = PermissionRegistry.GetAllCategories().ToList();
            return Task.FromResult<IActionResult>(Ok(categories));
        }

        /// <summary>
        /// Get permission by name
        /// </summary>
        [HttpGet("{name}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetPermission(string name)
        {
            var permission = PermissionRegistry.GetPermission(name);

            if (permission == null)
            {
                return Task.FromResult<IActionResult>(NotFound($"Permission '{name}' not found"));
            }

            var result = new
            {
                permission.Id,
                permission.Name,
                permission.Description,
                permission.Category,
                permission.Resource,
                permission.Action
            };

            return Task.FromResult<IActionResult>(Ok(result));
        }

        /// <summary>
        /// Check if user has specific permission
        /// </summary>
        [HttpGet("check/{permission}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public Task<IActionResult> CheckPermission(string permission)
        {
            var userId = GetCurrentUserId();

            // This would typically query the database for user permissions
            // For now, return a placeholder response
            var hasPermission = User.IsInRole("Admin") || User.IsInRole("SuperAdmin");

            return Task.FromResult<IActionResult>(Ok(new
            {
                userId,
                permission,
                hasPermission,
                message = hasPermission ? "Permission granted" : "Permission denied"
            }));
        }

        /// <summary>
        /// Get user's permissions
        /// </summary>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetUserPermissions(Guid userId)
        {
            // This would be implemented with a GetUserPermissionsQuery
            return Task.FromResult<IActionResult>(Ok(new { message = "GetUserPermissions endpoint - to be implemented" }));
        }

        /// <summary>
        /// Get role's permissions
        /// </summary>
        [HttpGet("role/{roleId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetRolePermissions(Guid roleId)
        {
            // This would be implemented with a GetRolePermissionsQuery
            return Task.FromResult<IActionResult>(Ok(new { message = "GetRolePermissions endpoint - to be implemented" }));
        }

        /// <summary>
        /// Get all permissions from database
        /// </summary>
        [HttpGet("database")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> GetAllPermissionsFromDatabase(
            [FromQuery] string? category = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] string? userType = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 100)
        {
            try
            {
                var query = new GetAllPermissionsFromDbQuery
                {
                    Category = category,
                    SearchTerm = searchTerm,
                    IsActive = isActive,
                    UserType = userType,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "An error occurred while retrieving permissions from database.", details = ex.Message });
            }
        }

        /// <summary>
        /// Get permission matrix for UI (categories vs permissions)
        /// </summary>
        [HttpGet("matrix")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public Task<IActionResult> GetPermissionMatrix()
        {
            var categories = PermissionRegistry.GetAllCategories().ToList();
            var matrix = new System.Collections.Generic.Dictionary<string, object>();

            foreach (var category in categories)
            {
                var categoryPermissions = PermissionRegistry.GetPermissionsByCategory(category);
                matrix[category] = categoryPermissions.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.Resource,
                    p.Action
                }).ToList();
            }

            return Task.FromResult<IActionResult>(Ok(new
            {
                categories,
                matrix,
                totalPermissions = PermissionRegistry.GetAllPermissions().Count()
            }));
        }

        /// <summary>
        /// Create a new permission
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> CreatePermission([FromBody] CreatePermissionCommand command)
        {
            try
            {
                var result = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetPermission), new { name = result.PermissionName }, result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "An error occurred while creating the permission.", details = ex.Message });
            }
        }

        /// <summary>
        /// Update an existing permission
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdatePermission(Guid id, [FromBody] UpdatePermissionCommand command)
        {
            try
            {
                command.Id = id;
                var result = await Mediator.Send(command);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "An error occurred while updating the permission.", details = ex.Message });
            }
        }

        /// <summary>
        /// Delete a permission
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeletePermission(Guid id)
        {
            try
            {
                var command = new DeletePermissionCommand { Id = id };
                var result = await Mediator.Send(command);
                return Ok(new { message = "Permission deleted successfully", deleted = result });
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "An error occurred while deleting the permission.", details = ex.Message });
            }
        }
    }
}
