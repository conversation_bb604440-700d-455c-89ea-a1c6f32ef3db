using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using System.Net.Http;
using System.Text.Json;
using Identity.Application.Common.Interfaces;

namespace Identity.Application.Services
{
    public class SubscriptionIntegrationService : ISubscriptionIntegrationService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<SubscriptionIntegrationService> _logger;
        private readonly string _subscriptionServiceBaseUrl;

        public SubscriptionIntegrationService(
            IHttpClientFactory httpClientFactory,
            ILogger<SubscriptionIntegrationService> logger,
            IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _subscriptionServiceBaseUrl = configuration.GetValue<string>("Services:SubscriptionManagement:BaseUrl") ?? "http://**************:5003";
        }

        public async Task<Guid?> GetUserSubscriptionPlanIdAsync(Guid userId)
        {
            try
            {
                _logger.LogInformation("Fetching subscription plan ID for user {UserId}", userId);

                using var httpClient = _httpClientFactory.CreateClient();
                var response = await httpClient.GetAsync($"{_subscriptionServiceBaseUrl}/api/subscriptions/user/{userId}/plan-id");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<SubscriptionPlanResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Successfully retrieved subscription plan ID {PlanId} for user {UserId}",
                        result?.PlanId, userId);

                    return result?.PlanId;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogInformation("No subscription found for user {UserId}", userId);
                    return null;
                }
                else
                {
                    _logger.LogWarning("Failed to get subscription plan ID for user {UserId}. Status: {StatusCode}",
                        userId, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching subscription plan ID for user {UserId}", userId);
                return null;
            }
        }

        public async Task<List<Guid>> GetPlanFeatureIdsAsync(Guid subscriptionPlanId)
        {
            try
            {
                _logger.LogInformation("Fetching feature IDs for subscription plan {PlanId}", subscriptionPlanId);

                using var httpClient = _httpClientFactory.CreateClient();
                var response = await httpClient.GetAsync($"{_subscriptionServiceBaseUrl}/api/plans/{subscriptionPlanId}/features");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var features = JsonSerializer.Deserialize<List<PlanFeatureResponse>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    var featureIds = features?.Where(f => f.IsEnabled).Select(f => f.FeatureId).ToList() ?? new List<Guid>();

                    _logger.LogInformation("Successfully retrieved {FeatureCount} feature IDs for plan {PlanId}",
                        featureIds.Count, subscriptionPlanId);

                    return featureIds;
                }
                else
                {
                    _logger.LogWarning("Failed to get feature IDs for plan {PlanId}. Status: {StatusCode}",
                        subscriptionPlanId, response.StatusCode);
                    return new List<Guid>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching feature IDs for plan {PlanId}", subscriptionPlanId);
                return new List<Guid>();
            }
        }

        public async Task<Dictionary<Guid, SubscriptionFeatureDetails>> GetFeatureFlagDetailsByIdsAsync(List<Guid> featureIds, string? userType = null)
        {
            try
            {
                if (!featureIds.Any())
                {
                    return new Dictionary<Guid, SubscriptionFeatureDetails>();
                }

                _logger.LogInformation("Fetching feature flag details for {FeatureCount} features with userType filter: {UserType}", featureIds.Count, userType);

                using var httpClient = _httpClientFactory.CreateClient();
                var featureIdsQuery = string.Join(",", featureIds);
                var url = $"{_subscriptionServiceBaseUrl}/api/FeatureFlags/by-ids?featureIds={featureIdsQuery}";

                // Add userType parameter if provided
                if (!string.IsNullOrEmpty(userType))
                {
                    url += $"&userType={Uri.EscapeDataString(userType)}";
                }

                _logger.LogInformation("Calling subscription service URL: {Url}", url);
                var response = await httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();

                    var featureFlags = JsonSerializer.Deserialize<List<FeatureFlagResponse>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    var result = featureFlags?.ToDictionary(
                        ff => ff.FeatureId,
                        ff => new SubscriptionFeatureDetails
                        {
                            FeatureId = ff.FeatureId,
                            FeatureName = ff.Name,
                            MenuId = ff.MenuId,
                            UserType = ff.UserType
                        }) ?? new Dictionary<Guid, SubscriptionFeatureDetails>();

                    _logger.LogInformation("Successfully retrieved {FeatureFlagCount} feature flag details", result.Count);

                    return result;
                }
                else
                {
                    _logger.LogWarning("Failed to get feature flag details. Status: {StatusCode}", response.StatusCode);
                    return new Dictionary<Guid, SubscriptionFeatureDetails>();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching feature flag details for {FeatureCount} features", featureIds.Count);
                return new Dictionary<Guid, SubscriptionFeatureDetails>();
            }
        }
    }

    // Response DTOs for subscription service integration
    public class SubscriptionPlanResponse
    {
        public Guid PlanId { get; set; }
    }

    public class PlanFeatureResponse
    {
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsEnabled { get; set; }
    }

    public class FeatureFlagResponse
    {
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public Guid? MenuId { get; set; }
        public string? UserType { get; set; }
    }
}
