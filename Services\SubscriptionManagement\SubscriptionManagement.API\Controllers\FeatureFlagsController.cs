using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreateFeatureFlag;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Services;
using System.Security.Claims;

namespace SubscriptionManagement.API.Controllers;

[ApiController]
[Route("api/[controller]")]
//[Authorize]
public class FeatureFlagsController : ControllerBase
{
    private readonly IFeatureFlagService _featureFlagService;
    private readonly IMediator _mediator;
    private readonly ILogger<FeatureFlagsController> _logger;

    public FeatureFlagsController(
        IFeatureFlagService featureFlagService,
        IMediator mediator,
        ILogger<FeatureFlagsController> logger)
    {
        _featureFlagService = featureFlagService;
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Check if a feature is enabled for the current user
    /// </summary>
    [HttpGet("{featureKey}/enabled")]
    public async Task<IActionResult> IsFeatureEnabled(string featureKey)
    {
        var userId = GetCurrentUserId();
        var context = GetUserContext();

        var isEnabled = await _featureFlagService.IsFeatureEnabledAsync(featureKey, userId, context);

        return Ok(new { featureKey, isEnabled, userId });
    }

    /// <summary>
    /// Get feature value for the current user
    /// </summary>
    [HttpGet("{featureKey}/value")]
    public async Task<IActionResult> GetFeatureValue(string featureKey, [FromQuery] string? defaultValue = null)
    {
        var userId = GetCurrentUserId();
        var context = GetUserContext();

        var value = await _featureFlagService.GetFeatureValueAsync(featureKey, userId, defaultValue, context);

        return Ok(new { featureKey, value, userId });
    }

    /// <summary>
    /// Get A/B test variant for the current user
    /// </summary>
    [HttpGet("{testKey}/variant")]
    public async Task<IActionResult> GetABTestVariant(string testKey)
    {
        var userId = GetCurrentUserId();
        var context = GetUserContext();

        var variant = await _featureFlagService.GetABTestVariantAsync(testKey, userId, context);

        return Ok(new { testKey, variant, userId });
    }

    /// <summary>
    /// Record a conversion for A/B testing
    /// </summary>
    [HttpPost("{featureKey}/conversion")]
    public async Task<IActionResult> RecordConversion(string featureKey, [FromBody] ConversionRequest request)
    {
        var userId = GetCurrentUserId();

        await _featureFlagService.RecordConversionAsync(featureKey, userId, request.Variant, request.ConversionData);

        return Ok(new { message = "Conversion recorded successfully" });
    }

    /// <summary>
    /// Get all feature flags for the current user
    /// </summary>
    [HttpGet("my-features")]
    public async Task<IActionResult> GetMyFeatures()
    {
        var userId = GetCurrentUserId();
        var context = GetUserContext();

        var features = await _featureFlagService.GetFeatureFlagsForUserAsync(userId, context);

        var result = features.Select(f => new
        {
            f.Key,
            f.Name,
            f.Description,
            f.Type,
            IsEnabled = f.IsActiveForUser(userId, context),
            Variant = f.GetVariantForUser(userId, context)
        });

        return Ok(result);
    }

    /// <summary>
    /// Get all feature flags (Admin only)
    /// </summary>
    [HttpGet]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetAllFeatureFlags()
    {
        var features = await _featureFlagService.GetAllFeatureFlagsAsync();

        var result = features.Select(f => new
        {
            f.Id,
            f.Key,
            f.Name,
            f.Description,
            f.Type,
            f.Status,
            f.RolloutPercentage,
            f.StartDate,
            f.EndDate,
            f.MenuId,
            RulesCount = f.Rules.Count,
            UsageCount = f.UsageHistory.Count
        });

        return Ok(result);
    }

    /// <summary>
    /// Get feature flags by IDs - used by Identity service for permission enrichment
    /// </summary>
    [HttpGet("by-ids")]
    public async Task<IActionResult> GetFeatureFlagsByIds([FromQuery] string featureIds, [FromQuery] string? userType = null)
    {
        try
        {
            if (string.IsNullOrEmpty(featureIds))
            {
                return BadRequest("FeatureIds parameter is required");
            }

            var featureIdList = featureIds.Split(',')
                .Where(id => Guid.TryParse(id.Trim(), out _))
                .Select(id => Guid.Parse(id.Trim()))
                .ToList();

            if (!featureIdList.Any())
            {
                return BadRequest("No valid feature IDs provided");
            }

            _logger.LogInformation("Getting feature flags for {Count} feature IDs with userType filter: {UserType}",
                featureIdList.Count, userType);

            var features = new List<object>();
            foreach (var featureId in featureIdList)
            {
                var feature = await _featureFlagService.GetFeatureFlagByIdAsync(featureId);
                if (feature != null)
                {
                    // Apply userType filter if provided
                    if (!string.IsNullOrEmpty(userType) && !string.IsNullOrEmpty(feature.UserType) &&
                        !feature.UserType.Equals(userType, StringComparison.OrdinalIgnoreCase))
                    {
                        continue; // Skip this feature as it doesn't match the userType filter
                    }

                    features.Add(new
                    {
                        FeatureId = feature.Id,
                        Name = feature.Name,
                        MenuId = feature.MenuId,
                        UserType = feature.UserType
                    });
                }
            }

            _logger.LogInformation("Successfully retrieved {Count} feature flags out of {RequestedCount} requested (after userType filtering)",
                features.Count, featureIdList.Count);

            return Ok(features);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature flags by IDs: {FeatureIds}, UserType: {UserType}", featureIds, userType);
            return StatusCode(500, new { message = "An error occurred while retrieving feature flags" });
        }
    }

    /// <summary>
    /// Create a new feature flag (Admin only)
    /// </summary>
    [HttpPost]
    // [Authorize(Roles = "Admin")]
    public async Task<IActionResult> CreateFeatureFlag([FromBody] CreateFeatureFlagRequest request)
    {
        try
        {
            var command = new CreateFeatureFlagCommand
            {
                Name = request.Name,
                Description = request.Description,
                Key = request.Key,
                Type = request.Type,
                DefaultValue = request.DefaultValue,
                MenuId = request.MenuId
            };

            var featureFlagId = await _mediator.Send(command);

            // Get the created feature flag to return in response
            var featureFlag = await _featureFlagService.GetFeatureFlagByIdAsync(featureFlagId);

            return CreatedAtAction(nameof(GetFeatureFlag), new { id = featureFlagId }, featureFlag);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        // catch (Exception ex)
        // {
        //     _logger.LogError(ex, "Error creating feature flag with key {Key}", request.Key);
        //     return StatusCode(500, new { message = "An error occurred while creating the feature flag" });
        // }

        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating feature flag with key {Key}", request.Key);
            return StatusCode(500, new
            {
                message = "An error occurred while creating the feature flag",
                exception = ex.Message,
                stackTrace = ex.StackTrace // Optional: include only in development environments
            });
        }
    }

    /// <summary>
    /// Get feature flag by ID (Admin only)
    /// </summary>
    [HttpGet("{id:guid}")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetFeatureFlag(Guid id)
    {
        var feature = await _featureFlagService.GetFeatureFlagByIdAsync(id);

        if (feature == null)
        {
            return NotFound();
        }

        return Ok(feature);
    }

    /// <summary>
    /// Update feature flag (Admin only)
    /// </summary>
    [HttpPut("{id:guid}")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateFeatureFlag(Guid id, [FromBody] UpdateFeatureFlagRequest request)
    {
        try
        {
            var featureFlag = await _featureFlagService.UpdateFeatureFlagAsync(
                id,
                request.Name,
                request.Description,
                request.Status,
                request.MenuId);

            return Ok(featureFlag);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Activate feature flag (Admin only)
    /// </summary>
    [HttpPost("{id:guid}/activate")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> ActivateFeatureFlag(Guid id, [FromBody] ActivateFeatureFlagRequest? request = null)
    {
        try
        {
            await _featureFlagService.ActivateFeatureFlagAsync(id, request?.StartDate, request?.EndDate);
            return Ok(new { message = "Feature flag activated successfully" });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Deactivate feature flag (Admin only)
    /// </summary>
    [HttpPost("{id:guid}/deactivate")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> DeactivateFeatureFlag(Guid id)
    {
        try
        {
            await _featureFlagService.DeactivateFeatureFlagAsync(id);
            return Ok(new { message = "Feature flag deactivated successfully" });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Update rollout percentage (Admin only)
    /// </summary>
    [HttpPut("{id:guid}/rollout")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> UpdateRollout(Guid id, [FromBody] UpdateRolloutRequest request)
    {
        try
        {
            await _featureFlagService.UpdateRolloutPercentageAsync(id, request.Percentage);
            return Ok(new { message = "Rollout percentage updated successfully" });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Get feature flag analytics (Admin only)
    /// </summary>
    [HttpGet("{id:guid}/analytics")]
    //  [Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetAnalytics(Guid id, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        try
        {
            var analytics = await _featureFlagService.GetFeatureFlagAnalyticsAsync(id, from, to);
            return Ok(analytics);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    /// <summary>
    /// Get A/B test results (Admin only)
    /// </summary>
    [HttpGet("{id:guid}/ab-test-results")]
    //[Authorize(Roles = "Admin")]
    public async Task<IActionResult> GetABTestResults(Guid id, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        try
        {
            var results = await _featureFlagService.GetABTestResultsAsync(id, from, to);
            return Ok(results);
        }
        catch (ArgumentException ex)
        {
            return NotFound(new { message = ex.Message });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (Guid.TryParse(userIdClaim, out var userId))
        {
            return userId;
        }
        throw new UnauthorizedAccessException("Invalid user ID in token");
    }

    private Dictionary<string, object> GetUserContext()
    {
        var context = new Dictionary<string, object>();

        // Add user claims as context
        foreach (var claim in User.Claims)
        {
            context[claim.Type] = claim.Value;
        }

        // Add request context
        context["userAgent"] = Request.Headers.UserAgent.ToString();
        context["ipAddress"] = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";

        return context;
    }
}

// Request/Response models
public class ConversionRequest
{
    public string? Variant { get; set; }
    public Dictionary<string, object>? ConversionData { get; set; }
}

public class CreateFeatureFlagRequest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public FeatureFlagType Type { get; set; }
    public string? DefaultValue { get; set; }
    public Guid? MenuId { get; set; } // Menu ID from UserManagement service
}

public class UpdateFeatureFlagRequest
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public FeatureFlagStatus? Status { get; set; }
    public Guid? MenuId { get; set; } // Menu ID from UserManagement service
}

public class ActivateFeatureFlagRequest
{
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

public class UpdateRolloutRequest
{
    public int Percentage { get; set; }
}
